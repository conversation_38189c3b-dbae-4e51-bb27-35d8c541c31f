{"name": "newproject", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "check": "npm run lint && npm run format:check"}, "dependencies": {"@imgly/background-removal": "^1.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.11", "lucide-react": "^0.533.0", "next": "15.4.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-color": "^2.19.3", "react-dom": "19.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-color": "^3.0.13", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}